{"templatePinStates": [], "dependencyTypeInfos": [{"userAdded": false, "type": "UnityEngine.AnimationClip", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEditor.Animations.AnimatorController", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.AnimatorOverrideController", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEditor.Audio.AudioMixerController", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.ComputeShader", "defaultInstantiationMode": 1}, {"userAdded": false, "type": "UnityEngine.Cubemap", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.GameObject", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEditor.LightingDataAsset", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.LightingSettings", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Material", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEditor.MonoScript", "defaultInstantiationMode": 1}, {"userAdded": false, "type": "UnityEngine.PhysicsMaterial", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.PhysicsMaterial2D", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Rendering.PostProcessing.PostProcessProfile", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Rendering.PostProcessing.PostProcessResources", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Rendering.VolumeProfile", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEditor.SceneAsset", "defaultInstantiationMode": 1}, {"userAdded": false, "type": "UnityEngine.Shader", "defaultInstantiationMode": 1}, {"userAdded": false, "type": "UnityEngine.ShaderVariantCollection", "defaultInstantiationMode": 1}, {"userAdded": false, "type": "UnityEngine.Texture", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Texture2D", "defaultInstantiationMode": 0}, {"userAdded": false, "type": "UnityEngine.Timeline.TimelineAsset", "defaultInstantiationMode": 0}], "defaultDependencyTypeInfo": {"userAdded": false, "type": "<default_scene_template_dependencies>", "defaultInstantiationMode": 1}, "newSceneOverride": 0}