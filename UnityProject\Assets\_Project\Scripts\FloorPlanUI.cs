using UnityEngine;
using UnityEngine.UI;

namespace Factory.Simulation
{
    /// <summary>
    /// Simple UI controller for floor plan import functionality
    /// This is a temporary UI that will be polished in UI-focused weeks
    /// </summary>
    public class FloorPlanUI : MonoBehaviour
    {
        [Header("UI References")]
        [Tooltip("Button to trigger floor plan import")]
        public Button importFloorPlanButton;
        
        [Toolt<PERSON>("Button to reset to original floor plan")]
        public Button resetFloorPlanButton;
        
        [<PERSON>lt<PERSON>("Optional text to show current floor plan status")]
        public Text statusText;

        [Header("Dependencies")]
        [Tooltip("Reference to the FloorPlanImporter component")]
        public FloorPlanImporter floorPlanImporter;

        private void Start()
        {
            SetupUI();
        }

        private void SetupUI()
        {
            // Validate dependencies
            if (floorPlanImporter == null)
            {
                floorPlanImporter = FindObjectOfType<FloorPlanImporter>();
                if (floorPlanImporter == null)
                {
                    Debug.LogError("FloorPlanUI: No FloorPlanImporter found in scene!");
                    return;
                }
            }

            // Setup button listeners
            if (importFloorPlanButton != null)
            {
                importFloorPlanButton.onClick.AddListener(OnImportFloorPlanClicked);
            }
            else
            {
                Debug.LogWarning("FloorPlanUI: Import button not assigned");
            }

            if (resetFloorPlanButton != null)
            {
                resetFloorPlanButton.onClick.AddListener(OnResetFloorPlanClicked);
            }

            // Update initial status
            UpdateStatusText("Ready to import floor plan");
        }

        private void OnImportFloorPlanClicked()
        {
            if (floorPlanImporter != null)
            {
                UpdateStatusText("Opening file dialog...");
                floorPlanImporter.ImportFloorPlan();
                
                // Update status after a brief delay (the import is async)
                Invoke(nameof(UpdateStatusAfterImport), 0.5f);
            }
        }

        private void OnResetFloorPlanClicked()
        {
            if (floorPlanImporter != null)
            {
                floorPlanImporter.ResetToOriginalFloorPlan();
                UpdateStatusText("Reset to original floor plan");
            }
        }

        private void UpdateStatusAfterImport()
        {
            UpdateStatusText("Floor plan import completed");
        }

        private void UpdateStatusText(string message)
        {
            if (statusText != null)
            {
                statusText.text = $"Floor Plan: {message}";
            }
            
            Debug.Log($"FloorPlanUI: {message}");
        }

        private void OnDestroy()
        {
            // Clean up button listeners
            if (importFloorPlanButton != null)
            {
                importFloorPlanButton.onClick.RemoveListener(OnImportFloorPlanClicked);
            }
            
            if (resetFloorPlanButton != null)
            {
                resetFloorPlanButton.onClick.RemoveListener(OnResetFloorPlanClicked);
            }
        }
    }
}
