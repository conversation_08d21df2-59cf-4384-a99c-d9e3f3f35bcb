{"document": {"title": "MVP Proposal: The Factory Design & Optimization Suite", "date": "July 8, 2025", "core_ideology": "We are building the first version of a professional engineering tool. Every feature must be implemented to a standard that inspires confidence and demonstrates undeniable value. The product's quality and intuitive power must speak for itself.", "guiding_okr": {"objective": "Deliver a stable, polished, and professional-grade Factory Design & Optimization Suite MVP that successfully secures a Letter of Intent from a pilot customer by September 1st."}, "final_mvp_features": [{"id": 1, "name": "Transparent Design Environment", "description": "A .dwg/.dxf importer with a two-click 'Scale Calibration' tool, and a grid-snapped 'Wall & Obstacle' placement toolkit."}, {"id": 2, "name": "High-Fidelity Simulation Engine", "description": "Configurable ScriptableObject 'Agent Profiles' loaded from external JSON files, a robust RobotController with a full autonomous task and energy cycle, and Accelerated Time Control."}, {"id": 3, "name": "Auditable Results & Executive Interface", "description": "A professional UI with a real-time congestion heatmap, the culminating Executive ROI Dashboard, and an auditable JSON Simulation Log export."}], "schedule": {"phases": [{"phase_number": 1, "name": "The Transparent Design & Simulation Foundation", "weeks": "3-5", "objective": "To establish a flexible design environment driven by verifiable data, and a functional, single-agent autonomous simulation.", "weekly_deliverables": [{"week": 3, "title": "The Design Toolkit", "date_range": "July 6 - July 13", "priority": "P1 (Limited Hours)", "focus": "Implementing the core features that make this a bespoke design tool.", "key_result": "Achieve 1:1 scale fidelity and enable accurate environment design.", "detailed_deliverables": [{"what": "A 'Scale Calibration' tool for imported CAD plans.", "how": "Implement a two-click 'reference line' method. A UI button enters 'Calibration Mode.' The first click stores point A, the second stores point B. A dialog box prompts for the real-world distance. The system calculates the required scale factor to match this distance in Unity units and resizes the ground plane texture."}, {"what": "A grid-snapped 'Wall & Obstacle' placement tool.", "how": "Use Physics.Raycast to get the mouse position on the ground plane. Before instantiating a wall prefab, round the position coordinates to the nearest whole or half unit (e.g., Mathf.Round(pos.x)) to achieve the snapping effect."}]}, {"week": 4, "title": "The Data-Driven Core Loop", "date_range": "July 14 - July 20", "priority": "P1 (Limited Hours)", "focus": "Building the 'brains' of the simulation and ensuring it's driven by transparent data from day one.", "key_result": "Validate the core simulation engine with a robot whose behavior is defined by external data.", "detailed_deliverables": [{"what": "A system to load Agent Profile stats from external JSON files.", "how": "Create a C# AgentProfile class with [System.Serializable] attributes. Use JsonUtility or a more robust library like Newtonsoft.Json to deserialize a .json file from a designated folder (e.g., StreamingAssets) into an instance of this class."}, {"what": "A functional RobotController State Machine that can complete a task.", "how": "Create an enum for states (IDLE, MOVING_TO_TASK). The robot, configured by its loaded JSON profile, will request a task from a singleton TaskManager, enter the MOVING_TO_TASK state, use NavMeshAgent.SetDestination(), and upon arrival, report completion to a singleton KPI_Manager."}]}, {"week": 5, "title": "The Autonomous Energy Cycle", "date_range": "July 21 - July 27", "priority": "P1 (Limited Hours), P2 Joins", "focus": "Implementing our key technological differentiator and onboarding P2 with a high-impact visual task.", "key_results": [{"assignee": "P1", "result": "Implement the robot's complete autonomous energy cycle.", "deliverable": {"what": "Battery depletion, BMH finding, queuing, and charging logic.", "how": "Deplete a currentBattery variable over time. When below a threshold, the robot enters a SEEKING_CHARGE state. It will query a new BMH_Manager singleton for the nearest available BMH (based on queue length), navigate there, and enter a 'charging' state simulated by a timer defined in its JSON profile."}}, {"assignee": "P2", "result": "Create a professional-grade selection system and UI foundation.", "deliverable": {"what": "A polished selection/highlight shader and the visual layout for the Simulation Setup Panel.", "how": "Find/create an outline shader and apply it to the robot's Visuals renderer on selection. Use Unity's UI Toolkit or Canvas to build the non-functional UI panel with sliders and buttons. This provides a clear visual target for later integration."}}]}]}, {"phase_number": 2, "name": "Full Steam - System Integration & Visualization", "weeks": "6-8", "objective": "To integrate all systems into a cohesive multi-agent simulation, build the high-impact visual feedback tools, and prepare the backend for data export.", "weekly_deliverables": [{"week": 6, "title": "System Integration & Interactive Controls", "date_range": "July 28 - Aug 3", "priority": "Full Steam", "focus": "Making the simulation fully interactive and configurable.", "key_results": [{"assignee": "P1", "result": "Fully instrument the code with KPI events (OnTaskCompleted, etc.) and upgrade the KPI_Manager.", "how": "Use C# Action or UnityEvent in the RobotController. The KPI_Manager subscribes to these static events to track advanced metrics without direct coupling."}, {"assignee": "P2", "result": "Integrate the Simulation Setup Panel UI.", "how": "Hook up the UI slider OnValueChanged events to public methods in a SimulationSettings script. This includes the crucial 'Accelerated Time' slider, which will control Time.timeScale. Implement the dropdown to select and load different robot JSON profiles."}]}, {"week": 7, "title": "Multi-Agent Systems & Heatmap", "date_range": "Aug 4 - Aug 10", "priority": "Full Steam", "focus": "Handling fleet dynamics and adding the primary visual diagnostic tool.", "key_results": [{"assignee": "P1", "result": "Implement the BMH_Manager queuing logic and refine multi-robot avoidance.", "how": "The BMH_Manager will maintain a List of all BMHs. When a robot needs to charge, it asks the manager, which finds the BMH with the shortest queue and returns its location."}, {"assignee": "P2", "result": "Implement the visual front-end for the congestion heatmap.", "how": "Create a simple plane that covers the floor with a custom heatmap shader. P1's backend logic will update a texture that this shader reads from, coloring pixels based on robot density or wait times."}]}, {"week": 8, "title": "'Render' Backend & Visual Polish", "date_range": "Aug 11 - Aug 17", "priority": "Full Steam", "focus": "Building the report generator and achieving a 'final look.'", "key_results": [{"assignee": "P1", "result": "Develop the backend logic for 'Render Mode' and JSON export.", "how": "Create a RunSimulation() coroutine that accelerates time, waits for a set duration, captures the final KPI_Manager stats into a SimulationResult data class, and serializes this class into a time-stamped JSON file."}, {"assignee": "P2", "result": "Replace all placeholder assets with professional final models and refine scene lighting.", "how": "Procure high-quality assets. Go through all prefabs and replace the placeholder visuals. Create a Post-Processing Volume to add professional effects like Ambient Occlusion and Bloom."}]}]}, {"phase_number": 3, "name": "The Executive Payoff & Finalization", "weeks": "9-10", "objective": "To build the culminating ROI dashboard, finalize the product, and prepare for a flawless investor presentation.", "weekly_deliverables": [{"week": 9, "title": "The Executive ROI Dashboard", "date_range": "Aug 18 - Aug 24", "priority": "Full Steam", "focus": "An intensive sprint on the single most important screen.", "key_result": "Deliver the final, client-facing Executive ROI Dashboard UI.", "implementation": [{"assignee": "P1", "how": "Create a DashboardUIController script that has a public Populate(SimulationResult result) method. This script will handle the logic of connecting the final data report to the UI elements."}, {"assignee": "P2", "how": "Design and build the final UI layout in Unity, focusing on clarity and professionalism. Hook up the Text elements, progress bars, etc., to the public fields in P1's DashboardUIController."}]}, {"week": 10, "title": "Finalization & Demo Prep", "date_range": "Aug 25 - Sep 1", "focus": "Ensuring a flawless delivery.", "key_result": "Produce a stable, bug-free, standalone build and a well-rehearsed investor presentation.", "work_type": "Paired Work", "description": "This entire week is dedicated to QA. Test every feature, hunt down every bug, and use the Profiler to ensure smooth performance. Practice the 'Design -> Configure (JSON) -> Render -> Analyze (Dashboard) -> Verify (JSON Log)' workflow until it is second nature. Build the final executable. This week's buffer is critical for success."}]}]}}}