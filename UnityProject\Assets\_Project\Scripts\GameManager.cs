using UnityEngine;

namespace Factory.Simulation
{
    public class GameManager : MonoBehaviour
    {
        // This makes the GameManager a "Singleton", meaning there's only one
        // and it can be easily accessed from any other script.
        public static GameManager Instance { get; private set; }

        // This holds our current mode. It starts in Selection mode by default.
        public EGameMode CurrentMode { get; private set; } = EGameMode.Selection;

        private void Awake()
        {
            // Singleton pattern setup
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
            }
            else
            {
                Instance = this;
            }
        }

        // Public method to allow other scripts to change the game mode.
        public void SetMode(EGameMode newMode)
        {
            CurrentMode = newMode;
            Debug.Log($"Game mode set to: {newMode}");
        }
    }
}