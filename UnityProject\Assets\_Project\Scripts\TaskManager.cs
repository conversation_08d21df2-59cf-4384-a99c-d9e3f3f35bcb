using UnityEngine;

namespace Factory.Simulation
{
    public class TaskManager : MonoBehaviour
    {
        public static TaskManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
            }
            else
            {
                Instance = this;
            }
        }

        // This is the public method robots will call to get a new task.
        // For now, it will just return a random point on the ground.
        public Vector3 RequestTask()
        {
            // In the future, this will pull from a sophisticated task queue.
            // For Week 4, a random destination is a perfect starting point.
            Vector3 randomPoint = new Vector3(Random.Range(-10f, 10f), 0, Random.Range(-10f, 10f));
            Debug.Log($"New task generated at destination: {randomPoint}");
            return randomPoint;
        }
    }
}