using UnityEngine;

namespace Factory.Simulation
{
    public class SelectionHighlight : MonoBehaviour
    {
        [Tooltip("The material to apply when this object is selected.")]
        [SerializeField] private Material highlightMaterial;

        private Renderer objectRenderer;
        private Material originalMaterial;
        private bool isHighlighted = false;

        private void Awake()
        {
            objectRenderer = GetComponentInChildren<Renderer>();
            if (objectRenderer != null)
            {
                originalMaterial = objectRenderer.material;
            }
            else
            {
                Debug.LogError($"SelectionHighlight on '{gameObject.name}' could not find a Renderer in its children.", this);
            }
        }

        public void SetHighlighted(bool highlighted)
        {
            if (objectRenderer == null || isHighlighted == highlighted) return;

            isHighlighted = highlighted;
            objectRenderer.material = highlighted ? highlightMaterial : originalMaterial;
        }
    }
}