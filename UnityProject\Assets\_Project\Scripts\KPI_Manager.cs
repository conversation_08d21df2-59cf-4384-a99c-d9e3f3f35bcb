using UnityEngine;

namespace Factory.Simulation
{
    public class KPI_Manager : MonoBehaviour
    {
        public static KPI_Manager Instance { get; private set; }

        // --- Key Performance Indicators ---
        public int TasksCompleted { get; private set; } = 0;
        // We will add more KPIs here later (e.g., Fleet Uptime, Total Travel Distance, etc.)

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
            }
            else
            {
                Instance = this;
            }
        }

        // This is the public method other scripts will call to report a completed task.
        public void TrackTaskCompletion(string robotName)
        {
            TasksCompleted++;
            Debug.Log($"KPI UPDATE: Task completed by '{robotName}'. Total tasks completed: {TasksCompleted}");
        }

        // We will add more tracking methods here later.
    }
}