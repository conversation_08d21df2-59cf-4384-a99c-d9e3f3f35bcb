using UnityEngine;
using System.IO; // Required for file operations

namespace Factory.Simulation
{
    public class ProfileLoader : MonoBehaviour
    {
        // This makes the ProfileLoader a Singleton for easy access.
        public static ProfileLoader Instance { get; private set; }

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
            }
            else
            {
                Instance = this;
            }
        }

        public AgentProfile LoadProfile(string profileName)
        {
            // Construct the full path to the JSON file in the StreamingAssets folder.
            string filePath = Path.Combine(Application.streamingAssetsPath, profileName + ".json");

            if (File.Exists(filePath))
            {
                // Read the JSON data from the file.
                string jsonData = File.ReadAllText(filePath);

                // Create a new ScriptableObject instance to hold the data.
                AgentProfile profile = ScriptableObject.CreateInstance<AgentProfile>();

                // Use Unity's built-in JsonUtility to deserialize the data into our object.
                JsonUtility.FromJsonOverwrite(jsonData, profile);

                Debug.Log($"Successfully loaded profile: {profile.agentModelName}");
                return profile;
            }
            else
            {
                Debug.LogError($"Profile loading failed: File not found at '{filePath}'");
                return null;
            }
        }
    }
}