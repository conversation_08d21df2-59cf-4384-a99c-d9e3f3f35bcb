using UnityEngine;
using UnityEngine.AI; // Needed for NavMeshAgent
using Unity.AI.Navigation; // <-- THE FIX: This is the correct namespace for NavMeshSurface

namespace Factory.Simulation
{
    public class ObjectPlacementManager : MonoBehaviour
    {
        [Header("Object Prefabs")]
        public GameObject robotPrefab;
        public GameObject bmhPrefab;
        public GameObject obstaclePrefab;

        [Header("Placement Settings")]
        public Material ghostMaterial;
        public float gridSize = 0.5f;
        // --- NEW: LayerMask for efficient raycasting ---
        [<PERSON>lt<PERSON>("Set this to the 'Ground' layer to ensure raycasts are efficient.")]
        [SerializeField] private LayerMask groundLayerMask;

        [Header("Scale Calibration")]
        [Tooltip("The parent object of the floor plan and all obstacles. This is what gets scaled.")]
        public Transform environmentContainer;
        [Tooltip("For demo purposes: Enter the known real-world distance for the two-point calibration here.")]
        public float knownRealWorldDistance = 10f;

        private bool isCalibrated = false;
      
        private Vector3 firstCalibPoint;
        private Vector3 secondCalibPoint;

        [Head<PERSON>("Navigation")]
        [Tooltip("Assign the NavMeshSurface component from your Ground object here.")]
        public NavMeshSurface navMeshSurface;

        private GameObject objectToPlace;
        private GameObject ghostObject;
        private Camera mainCamera;

        [Header("Floor Plan Import")]
        [Tooltip("The ground plane renderer for floor plan display (should be 'sample_Floorplan')")]
        public Renderer floorPlanRenderer;
        [Tooltip("Maximum texture size to prevent memory issues")]
        public int maxFloorPlanTextureSize = 2048;

        // --- PUBLIC METHODS for UI Buttons ---

        public void SelectRobotToPlace()
        {
            objectToPlace = robotPrefab;
            InstantiateGhostObject();
            GameManager.Instance.SetMode(EGameMode.Placement);
            Debug.Log("Selected Robot for placement.");
        }

        public void SelectBMHToPlace()
        {
            objectToPlace = bmhPrefab;
            InstantiateGhostObject();
            GameManager.Instance.SetMode(EGameMode.Placement);
            Debug.Log("Selected BMH for placement.");
        }

        public void SelectObstacleToPlace()
        {
            if (!isCalibrated)
            {
                Debug.LogWarning("ACTION REQUIRED: Please calibrate the scale first using the 'Calibrate Scale' tool. This ensures grid-snapping is accurate.");
                return;
            }

            if (obstaclePrefab == null)
            {
                Debug.LogError("Obstacle Prefab is not assigned in the ObjectPlacementManager!");
                return;
            }
            objectToPlace = obstaclePrefab;
            InstantiateGhostObject();
            GameManager.Instance.SetMode(EGameMode.Placement);
            Debug.Log("Selected Obstacle for placement.");
        }

        public void StartCalibration()
        {
            if (environmentContainer == null)
            {
                Debug.LogError("Environment Container is not assigned! Cannot start calibration.");
                return;
            }

            firstCalibPoint = Vector3.zero;
            secondCalibPoint = Vector3.zero;
            GameManager.Instance.SetMode(EGameMode.Calibration);
            Debug.Log("Calibration Mode Started. Click the first point of a known distance.");
        }

        // --- NEW: Floor Plan Import Methods for UI Buttons ---

        /// <summary>
        /// Public method for UI button - Import floor plan image
        /// </summary>
        public void ImportFloorPlan()
        {
            if (floorPlanMaterial == null)
            {
                Debug.LogError("ObjectPlacementManager: Floor plan system not initialized!");
                return;
            }

            string filePath = GetImageFilePath();
            if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
            {
                StartCoroutine(LoadFloorPlanCoroutine(filePath));
            }
        }

        /// <summary>
        /// Public method for UI button - Reset to original floor plan
        /// </summary>
        public void ResetFloorPlan()
        {
            if (floorPlanMaterial != null && originalFloorPlanTexture != null)
            {
                floorPlanMaterial.SetTexture("_MainTex", originalFloorPlanTexture);
                Debug.Log("ObjectPlacementManager: Reset to original floor plan");
            }
        }

        public void CompleteCalibration(float knownDistance)
        {
            if (knownDistance <= 0)
            {
                Debug.LogError($"Invalid Input: Calibration distance must be a positive number. You entered '{knownDistance}'. Aborting calibration.");
                GameManager.Instance.SetMode(EGameMode.Selection);
                return;
            }

            if (firstCalibPoint == Vector3.zero || secondCalibPoint == Vector3.zero)
            {
                Debug.LogError("Internal Error: Calibration points not set. Aborting.");
                GameManager.Instance.SetMode(EGameMode.Selection);
                return;
            }

            float unityDistance = Vector3.Distance(firstCalibPoint, secondCalibPoint);

            if (unityDistance < 0.01f)
            {
                Debug.LogWarning("Calibration Failed: The two points selected are too close together. Please try again with a larger distance.");
                GameManager.Instance.SetMode(EGameMode.Selection);
                return;
            }

            float requiredScale = knownDistance / unityDistance;
            environmentContainer.localScale = Vector3.one * requiredScale;
            Debug.Log($"CALIBRATION COMPLETE. Real distance '{knownDistance}m' applied. New scale: {requiredScale}");
            
            isCalibrated = true;
            GameManager.Instance.SetMode(EGameMode.Selection);
        }
        
        public void BakeNavigation()
        {
            if (navMeshSurface == null)
            {
                Debug.LogError("NavMeshSurface is not assigned in the ObjectPlacementManager!");
                return;
            }
            Debug.Log("Baking NavMesh...");
            navMeshSurface.BuildNavMesh();
            Debug.Log("NavMesh baking complete. Robots will now avoid new obstacles.");
        }


        // --- UNITY METHODS ---

        private Quaternion ghostRotation = Quaternion.identity;
        private Material floorPlanMaterial;
        private Texture2D originalFloorPlanTexture;

        private void Start()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
                Debug.LogWarning("ObjectPlacementManager: Camera.main was null, using FindObjectOfType<Camera>() as fallback");
            }

            // Initialize floor plan system
            InitializeFloorPlanSystem();
        }

        private void InitializeFloorPlanSystem()
        {
            if (floorPlanRenderer != null)
            {
                floorPlanMaterial = floorPlanRenderer.material;
                originalFloorPlanTexture = floorPlanMaterial.GetTexture("_MainTex") as Texture2D;
                Debug.Log("ObjectPlacementManager: Floor plan system initialized");
            }
            else
            {
                Debug.LogWarning("ObjectPlacementManager: Floor plan renderer not assigned - import functionality will not work");
            }
        }
        
        private void Update()
        {
            // --- NEW: Global Exit Key for this manager ---
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                // Only act if we are currently in a mode that this manager controls.
                if (GameManager.Instance.CurrentMode == EGameMode.Placement)
                {
                    // Clean up the ghost object and exit to Selection mode.
                    if (ghostObject != null)
                    {
                        Destroy(ghostObject);
                        ghostObject = null;
                        objectToPlace = null;
                        ghostRotation = Quaternion.identity;
                    }
                    GameManager.Instance.SetMode(EGameMode.Selection);
                    Debug.Log("Exited Placement Mode.");
                    return; // Stop processing for this frame.
                }
            }

            // --- Existing Mode Logic ---
            // This part remains the same.
            switch (GameManager.Instance.CurrentMode)
            {
                case EGameMode.Placement:
                    if (ghostObject != null) HandlePlacement();
                    break;
                case EGameMode.Calibration:
                    HandleCalibrationInput();
                    break;
                // The 'Translate' mode is handled by SelectionManager, so no case is needed here.
            }
        }

        private void HandleCalibrationInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                // --- MODIFIED: Added layerMask parameter ---
                if (mainCamera == null) mainCamera = Camera.main ?? FindObjectOfType<Camera>();
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, groundLayerMask))
                {
                    if (firstCalibPoint == Vector3.zero)
                    {
                        firstCalibPoint = hit.point;
                        Debug.Log($"Calibration: First point set at {firstCalibPoint}. Click the second point.");
                    }
                    else
                    {
                        secondCalibPoint = hit.point;
                        Debug.Log($"Calibration: Second point set at {secondCalibPoint}.");
                        // We now call CompleteCalibration using our public Inspector variable.
                        CompleteCalibration(knownRealWorldDistance);
                    }
                }
            }
        }

        private void HandlePlacement()
        {
            // --- MODIFIED: Added layerMask parameter ---
            if (mainCamera == null) mainCamera = Camera.main ?? FindObjectOfType<Camera>();
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, groundLayerMask))
            {
                Vector3 snappedPosition = GetSnappedPosition(hit.point);
                ghostObject.transform.position = snappedPosition;

                if (Input.GetKeyDown(KeyCode.R))
                {
                    float rotationAmount = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift) ? -90f : 90f;
                    ghostRotation *= Quaternion.Euler(0, rotationAmount, 0);
                }
                ghostObject.transform.rotation = ghostRotation;

                if (Input.GetMouseButtonDown(0))
                {
                    PlaceObject();
                }
            }
        }


        // Replace the entire method in your ObjectPlacementManager.cs script with this version.
        private Vector3 GetSnappedPosition(Vector3 originalWorldPosition)
        {
            // Only snap if we are placing an obstacle. Robots/BMHs can be placed freely.
            if (objectToPlace != obstaclePrefab)
            {
                return originalWorldPosition;
            }

            // --- THE FIX: Scale-Aware Snapping ---

            // 1. Convert the world-space mouse position into the container's local space.
            Vector3 localPos = environmentContainer.InverseTransformPoint(originalWorldPosition);

            // 2. Perform the snapping calculation in local space.
            //    Here, the gridSize is now relative to the container's own coordinate system.
            float snappedX = Mathf.Round(localPos.x / gridSize) * gridSize;
            float snappedZ = Mathf.Round(localPos.z / gridSize) * gridSize;

            // 3. Create the new snapped position in local space.
            Vector3 snappedLocalPos = new Vector3(snappedX, localPos.y, snappedZ);

            // 4. Convert the snapped local position back to world space to place the object.
            return environmentContainer.TransformPoint(snappedLocalPos);
        }
                
        private void InstantiateGhostObject()
        {
            if (ghostObject != null) Destroy(ghostObject);

            // --- THE CORRECT FIX ---
            // First, determine if this object should be parented to the scaled container.
            Transform parent = (objectToPlace == obstaclePrefab) ? environmentContainer : null;

            // Now, instantiate the ghost using the overload that accepts a parent.
            // This ensures it inherits the parent's scale correctly from the moment of creation.
            ghostObject = Instantiate(objectToPlace, parent);
            // --- END FIX ---


            // This excellent component-stripping logic you wrote remains the same.
            if (ghostObject.TryGetComponent(out UnityEngine.AI.NavMeshAgent agent)) agent.enabled = false;
            if (ghostObject.TryGetComponent(out RobotController controller)) controller.enabled = false;
            foreach (var col in ghostObject.GetComponentsInChildren<Collider>()) col.enabled = false;
            foreach (var rend in ghostObject.GetComponentsInChildren<Renderer>()) rend.material = ghostMaterial;
        }

       // Replace the PlaceObject method in ObjectPlacementManager.cs
private void PlaceObject()
{
    Transform parent = (objectToPlace == obstaclePrefab) ? environmentContainer : null;
    
    // --- MODIFIED: We now store a reference to the newly created object.
    GameObject newObject = Instantiate(objectToPlace, ghostObject.transform.position, ghostRotation, parent);

    // --- NEW: If the placed object is a robot, load a profile and initialize it.
    if (newObject.TryGetComponent(out RobotController robotController))
    {
        // For now, we'll hardcode loading the "QuickSwap_Profile".
        // Later, a UI dropdown will let the user choose.
        AgentProfile loadedProfile = ProfileLoader.Instance.LoadProfile("QuickSwap_Profile");
        robotController.Initialize(loadedProfile);
    }
}

        // --- FLOOR PLAN IMPORT HELPER METHODS ---

        private string GetImageFilePath()
        {
#if UNITY_EDITOR
            return UnityEditor.EditorUtility.OpenFilePanel("Select Floor Plan Image", "", "png,jpg,jpeg");
#else
            try
            {
                using (var openFileDialog = new System.Windows.Forms.OpenFileDialog())
                {
                    openFileDialog.Filter = "Image Files|*.png;*.jpg;*.jpeg";
                    openFileDialog.Title = "Select Floor Plan Image";
                    openFileDialog.Multiselect = false;

                    if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                    {
                        return openFileDialog.FileName;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"ObjectPlacementManager: Error opening file dialog: {e.Message}");
            }
            return "";
#endif
        }

        private System.Collections.IEnumerator LoadFloorPlanCoroutine(string filePath)
        {
            Debug.Log($"ObjectPlacementManager: Loading floor plan from {filePath}");

            byte[] imageData;
            try
            {
                imageData = System.IO.File.ReadAllBytes(filePath);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"ObjectPlacementManager: Error reading file: {e.Message}");
                yield break;
            }

            Texture2D newTexture = new Texture2D(2, 2, TextureFormat.RGB24, false);
            newTexture.name = System.IO.Path.GetFileNameWithoutExtension(filePath);

            if (newTexture.LoadImage(imageData))
            {
                if (newTexture.width > maxFloorPlanTextureSize || newTexture.height > maxFloorPlanTextureSize)
                {
                    newTexture = ResizeFloorPlanTexture(newTexture, maxFloorPlanTextureSize);
                }

                floorPlanMaterial.SetTexture("_MainTex", newTexture);
                Debug.Log($"ObjectPlacementManager: Successfully loaded floor plan '{newTexture.name}' ({newTexture.width}x{newTexture.height})");
            }
            else
            {
                Debug.LogError("ObjectPlacementManager: Failed to load image data into texture");
                Destroy(newTexture);
            }

            yield return null;
        }

        private Texture2D ResizeFloorPlanTexture(Texture2D originalTexture, int maxSize)
        {
            int newWidth = originalTexture.width;
            int newHeight = originalTexture.height;

            if (newWidth > maxSize || newHeight > maxSize)
            {
                float aspectRatio = (float)newWidth / newHeight;

                if (newWidth > newHeight)
                {
                    newWidth = maxSize;
                    newHeight = Mathf.RoundToInt(maxSize / aspectRatio);
                }
                else
                {
                    newHeight = maxSize;
                    newWidth = Mathf.RoundToInt(maxSize * aspectRatio);
                }
            }

            RenderTexture rt = RenderTexture.GetTemporary(newWidth, newHeight);
            Graphics.Blit(originalTexture, rt);

            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = rt;

            Texture2D resizedTexture = new Texture2D(newWidth, newHeight);
            resizedTexture.ReadPixels(new Rect(0, 0, newWidth, newHeight), 0, 0);
            resizedTexture.Apply();

            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(rt);

            Destroy(originalTexture);

            Debug.Log($"ObjectPlacementManager: Resized texture to {newWidth}x{newHeight}");
            return resizedTexture;
        }
    }
}