using System.Collections.Generic;
using System.IO;
using UnityEngine;
using TMPro;
using System.Collections;

namespace Factory.Simulation
{
    /// <summary>
    /// Modern FloorPlan Manager - Updated for Unity 2025
    /// Manages floor plan library - importing, storing, and applying floor plans
    /// Two-step process: Import to library, then select and apply to scene
    /// </summary>
    public class FloorPlanManager : MonoBehaviour
    {
        [Header("Floor Plan Settings")]
        [Tooltip("The ground plane renderer that displays the active floor plan")]
        public Renderer floorPlanRenderer;

        [Tooltip("TMP Dropdown UI for selecting floor plans")]
        public TMP_Dropdown floorPlanDropdown;

        [Tooltip("Maximum texture size for imported floor plans")]
        public int maxTextureSize = 2048;

        [Tooltip("Texture property name (usually '_MainTex' or '_BaseMap')")]
        public string texturePropertyName = "_BaseMap";

        [Header("Paths")]
        [Tooltip("StreamingAssets folder for persistent floor plan storage")]
        public string streamingAssetsFloorPlanPath = "FloorPlans";
        
        [Tooltip("Project folder with default floor plans")]
        public string projectFloorPlanPath = "Assets/_Project/Art/Textures/Floorplans";

        [Header("UI References")]
        [Tooltip("Button to import floor plans")]
        public UnityEngine.UI.Button importButton;
        
        [Tooltip("Button to delete selected floor plan")]
        public UnityEngine.UI.Button deleteButton;
        
        [Tooltip("Button to apply selected floor plan")]
        public UnityEngine.UI.Button applyButton;

        private Material floorPlanMaterial;
        private List<FloorPlanData> availableFloorPlans = new List<FloorPlanData>();
        private FloorPlanData currentFloorPlan;
        private string streamingAssetsFullPath;

        [System.Serializable]
        public class FloorPlanData
        {
            public string name;
            public string filePath;
            public bool isDefault; // True for built-in floor plans, false for imported ones
            
            public FloorPlanData(string name, string filePath, bool isDefault = false)
            {
                this.name = name;
                this.filePath = filePath;
                this.isDefault = isDefault;
            }
        }

        private void Start()
        {
            InitializeFloorPlanSystem();
        }

        private void InitializeFloorPlanSystem()
        {
            // Setup paths
            streamingAssetsFullPath = Path.Combine(Application.streamingAssetsPath, streamingAssetsFloorPlanPath);

            // Create StreamingAssets floor plan directory if it doesn't exist
            if (!Directory.Exists(streamingAssetsFullPath))
            {
                Directory.CreateDirectory(streamingAssetsFullPath);
                Debug.Log($"FloorPlanManager: Created directory {streamingAssetsFullPath}");
            }

            // Initialize material reference
            if (floorPlanRenderer != null)
            {
                floorPlanMaterial = floorPlanRenderer.material;
                Debug.Log($"FloorPlanManager: Using material '{floorPlanMaterial.name}' on renderer '{floorPlanRenderer.name}'");

                // Debug current texture
                Texture currentTexture = floorPlanMaterial.GetTexture(texturePropertyName);
                Debug.Log($"FloorPlanManager: Current texture on property '{texturePropertyName}': {(currentTexture ? currentTexture.name : "NULL")}");
            }
            else
            {
                Debug.LogError("FloorPlanManager: Floor plan renderer not assigned!");
                return;
            }

            // Setup UI button listeners
            SetupUIButtons();
            
            // Load available floor plans
            RefreshFloorPlanLibrary();
            
            // Setup dropdown
            SetupDropdown();
            
            Debug.Log($"FloorPlanManager: Initialized with {availableFloorPlans.Count} floor plans");
        }

        private void SetupUIButtons()
        {
            if (importButton != null)
                importButton.onClick.AddListener(ImportFloorPlan);
            
            if (deleteButton != null)
                deleteButton.onClick.AddListener(DeleteSelectedFloorPlan);
            
            if (applyButton != null)
                applyButton.onClick.AddListener(ApplySelectedFloorPlan);
        }

        /// <summary>
        /// Refresh the library by scanning both default and imported floor plans
        /// </summary>
        private void RefreshFloorPlanLibrary()
        {
            availableFloorPlans.Clear();

            // Load default floor plans from project folder
            LoadDefaultFloorPlans();
            
            // Load imported floor plans from StreamingAssets
            LoadImportedFloorPlans();
        }

        private void LoadDefaultFloorPlans()
        {
            if (Directory.Exists(projectFloorPlanPath))
            {
                string[] files = Directory.GetFiles(projectFloorPlanPath, "*.png");
                foreach (string file in files)
                {
                    string fileName = Path.GetFileNameWithoutExtension(file);
                    availableFloorPlans.Add(new FloorPlanData(fileName + " (Default)", file, true));
                }
                Debug.Log($"FloorPlanManager: Loaded {files.Length} default floor plans");
            }
        }

        private void LoadImportedFloorPlans()
        {
            if (Directory.Exists(streamingAssetsFullPath))
            {
                string[] files = Directory.GetFiles(streamingAssetsFullPath, "*.png");
                foreach (string file in files)
                {
                    string fileName = Path.GetFileNameWithoutExtension(file);
                    availableFloorPlans.Add(new FloorPlanData(fileName, file, false));
                }
                Debug.Log($"FloorPlanManager: Loaded {files.Length} imported floor plans");
            }
        }

        private void SetupDropdown()
        {
            if (floorPlanDropdown == null)
            {
                Debug.LogWarning("FloorPlanManager: TMP_Dropdown not assigned");
                return;
            }

            // Clear existing options
            floorPlanDropdown.ClearOptions();
            
            // Add floor plan names to dropdown
            List<string> options = new List<string>();
            foreach (var floorPlan in availableFloorPlans)
            {
                options.Add(floorPlan.name);
            }
            
            floorPlanDropdown.AddOptions(options);
            
            // Set up listener for dropdown changes
            floorPlanDropdown.onValueChanged.RemoveAllListeners();
            floorPlanDropdown.onValueChanged.AddListener(OnFloorPlanSelected);
            
            // Select first floor plan if available
            if (availableFloorPlans.Count > 0)
            {
                floorPlanDropdown.value = 0;
                OnFloorPlanSelected(0);
            }
        }

        // --- PUBLIC METHODS FOR UI BUTTONS ---

        /// <summary>
        /// Import a new floor plan from user's computer to the library
        /// Modern approach using Unity's file browser
        /// </summary>
        public void ImportFloorPlan()
        {
            ShowModernFileImport();
        }

        /// <summary>
        /// Delete the currently selected floor plan (with confirmation)
        /// </summary>
        public void DeleteSelectedFloorPlan()
        {
            if (currentFloorPlan == null)
            {
                Debug.LogWarning("FloorPlanManager: No floor plan selected for deletion");
                return;
            }

            if (currentFloorPlan.isDefault)
            {
                Debug.LogWarning("FloorPlanManager: Cannot delete default floor plans");
                return;
            }

            // For now, we'll use a simple confirmation via Debug.Log
            // In a real implementation, you'd want to show a proper UI dialog
            Debug.Log($"FloorPlanManager: Deleting floor plan '{currentFloorPlan.name}'");
            DeleteFloorPlan(currentFloorPlan);
        }

        /// <summary>
        /// Apply the selected floor plan to the scene
        /// </summary>
        public void ApplySelectedFloorPlan()
        {
            if (currentFloorPlan != null)
            {
                StartCoroutine(LoadFloorPlanToScene(currentFloorPlan));
            }
        }

        // --- PRIVATE HELPER METHODS ---

        private void OnFloorPlanSelected(int index)
        {
            if (index >= 0 && index < availableFloorPlans.Count)
            {
                currentFloorPlan = availableFloorPlans[index];
                Debug.Log($"FloorPlanManager: Selected floor plan '{currentFloorPlan.name}'");
                
                // Update delete button state
                if (deleteButton != null)
                {
                    deleteButton.interactable = !currentFloorPlan.isDefault;
                }
            }
        }



        private void DeleteFloorPlan(FloorPlanData floorPlan)
        {
            try
            {
                if (File.Exists(floorPlan.filePath))
                {
                    File.Delete(floorPlan.filePath);
                    Debug.Log($"FloorPlanManager: Deleted floor plan '{floorPlan.name}'");

                    // Refresh library and dropdown
                    RefreshFloorPlanLibrary();
                    SetupDropdown();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FloorPlanManager: Error deleting floor plan: {e.Message}");
            }
        }

        private IEnumerator LoadFloorPlanToScene(FloorPlanData floorPlan)
        {
            Debug.Log($"FloorPlanManager: Loading floor plan '{floorPlan.name}' to scene");

            byte[] imageData;
            try
            {
                imageData = File.ReadAllBytes(floorPlan.filePath);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FloorPlanManager: Error reading floor plan file: {e.Message}");
                yield break;
            }

            Texture2D newTexture = new Texture2D(2, 2, TextureFormat.RGB24, false);
            newTexture.name = floorPlan.name;

            if (newTexture.LoadImage(imageData))
            {
                // Resize if necessary
                if (newTexture.width > maxTextureSize || newTexture.height > maxTextureSize)
                {
                    newTexture = ResizeTexture(newTexture, maxTextureSize);
                }

                // Debug before applying
                Texture oldTexture = floorPlanMaterial.GetTexture(texturePropertyName);
                Debug.Log($"FloorPlanManager: Before apply - Old texture: {(oldTexture ? oldTexture.name : "NULL")}");

                // Apply to material
                floorPlanMaterial.SetTexture(texturePropertyName, newTexture);

                // Fix tiling - ensure texture appears once, not repeated
                floorPlanMaterial.SetTextureScale(texturePropertyName, Vector2.one);
                floorPlanMaterial.SetTextureOffset(texturePropertyName, Vector2.zero);

                // Debug after applying
                Texture verifyTexture = floorPlanMaterial.GetTexture(texturePropertyName);
                Debug.Log($"FloorPlanManager: After apply - New texture: {(verifyTexture ? verifyTexture.name : "NULL")}");
                Debug.Log($"FloorPlanManager: Applied floor plan '{floorPlan.name}' to scene ({newTexture.width}x{newTexture.height}) using property '{texturePropertyName}'");
            }
            else
            {
                Debug.LogError($"FloorPlanManager: Failed to load image data for '{floorPlan.name}'");
                Destroy(newTexture);
            }

            yield return null;
        }

        private Texture2D ResizeTexture(Texture2D originalTexture, int maxSize)
        {
            int newWidth = originalTexture.width;
            int newHeight = originalTexture.height;

            if (newWidth > maxSize || newHeight > maxSize)
            {
                float aspectRatio = (float)newWidth / newHeight;

                if (newWidth > newHeight)
                {
                    newWidth = maxSize;
                    newHeight = Mathf.RoundToInt(maxSize / aspectRatio);
                }
                else
                {
                    newHeight = maxSize;
                    newWidth = Mathf.RoundToInt(maxSize * aspectRatio);
                }
            }

            RenderTexture rt = RenderTexture.GetTemporary(newWidth, newHeight);
            Graphics.Blit(originalTexture, rt);

            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = rt;

            Texture2D resizedTexture = new Texture2D(newWidth, newHeight);
            resizedTexture.ReadPixels(new Rect(0, 0, newWidth, newHeight), 0, 0);
            resizedTexture.Apply();

            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(rt);

            Destroy(originalTexture);

            return resizedTexture;
        }

        // --- MODERN FILE IMPORT ALTERNATIVES ---
        
        /// <summary>
        /// Modern approach: Use Unity's built-in file browser or custom drag-drop
        /// </summary>
        public void ShowModernFileImport()
        {
            // Option 1: Use Unity's built-in file browser (Editor only)
            #if UNITY_EDITOR
            string path = UnityEditor.EditorUtility.OpenFilePanel("Select Floor Plan", "", "png,jpg,jpeg");
            if (!string.IsNullOrEmpty(path))
            {
                StartCoroutine(ProcessImportedFile(path));
            }
            #endif
            
            // Option 2: For builds, consider using Unity's RuntimeFileBrowser asset
            // or implementing a custom drag-drop system
        }
        
        private IEnumerator ProcessImportedFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                yield break;
                
            string fileName = Path.GetFileName(filePath);
            string destinationPath = Path.Combine(streamingAssetsFullPath, fileName);

            // Check if file already exists
            if (File.Exists(destinationPath))
            {
                Debug.LogWarning($"FloorPlanManager: Floor plan '{fileName}' already exists in library");
                yield break;
            }

            try
            {
                // Copy file to StreamingAssets
                File.Copy(filePath, destinationPath);
                Debug.Log($"FloorPlanManager: Imported floor plan '{fileName}' to library");

                // Refresh library and dropdown
                RefreshFloorPlanLibrary();
                SetupDropdown();

                // Select the newly imported floor plan
                for (int i = 0; i < availableFloorPlans.Count; i++)
                {
                    if (availableFloorPlans[i].filePath == destinationPath)
                    {
                        floorPlanDropdown.value = i;
                        OnFloorPlanSelected(i);
                        break;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FloorPlanManager: Error importing floor plan: {e.Message}");
            }
        }
    }
}