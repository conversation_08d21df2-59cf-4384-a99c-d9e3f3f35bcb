using UnityEngine;
using System.IO;
using System.Collections;

namespace Factory.Simulation
{
    public class FloorPlanImporter : MonoBehaviour
    {
        [Head<PERSON>("Floor Plan Settings")]
        [Tooltip("The ground plane renderer that will display the imported floor plan")]
        public Renderer groundPlaneRenderer;
        
        [Tooltip("Default material property name for the main texture (usually '_MainTex' or '_BaseMap')")]
        public string texturePropertyName = "_MainTex";
        
        [Header("Import Settings")]
        [Tooltip("Maximum texture size to prevent memory issues")]
        public int maxTextureSize = 2048;
        
        [Tooltip("Should the texture be readable? (Required for some operations but uses more memory)")]
        public bool makeTextureReadable = false;

        private Material groundMaterial;
        private Texture2D originalTexture;

        private void Start()
        {
            // Cache the ground material and original texture
            if (groundPlaneRenderer != null)
            {
                groundMaterial = groundPlaneRenderer.material;
                originalTexture = groundMaterial.GetTexture(texturePropertyName) as Texture2D;
            }
            else
            {
                Debug.LogError("FloorPlanImporter: Ground plane renderer not assigned!");
            }
        }

        /// <summary>
        /// Public method to be called by UI button - opens file dialog and imports PNG
        /// </summary>
        public void ImportFloorPlan()
        {
            if (groundMaterial == null)
            {
                Debug.LogError("FloorPlanImporter: Ground material not found!");
                return;
            }

            // Open file dialog
            string filePath = GetImageFilePath();
            
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                StartCoroutine(LoadImageCoroutine(filePath));
            }
            else if (!string.IsNullOrEmpty(filePath))
            {
                Debug.LogError($"FloorPlanImporter: File not found: {filePath}");
            }
            // If filePath is empty, user cancelled the dialog
        }

        /// <summary>
        /// Opens a file dialog to select PNG image file
        /// </summary>
        private string GetImageFilePath()
        {
#if UNITY_EDITOR
            // In editor, use Unity's built-in file panel
            return UnityEditor.EditorUtility.OpenFilePanel(
                "Select Floor Plan Image", 
                "", 
                "png,jpg,jpeg"
            );
#else
            // In build, use Windows Forms (Windows only for now)
            try
            {
                using (var openFileDialog = new System.Windows.Forms.OpenFileDialog())
                {
                    openFileDialog.Filter = "Image Files|*.png;*.jpg;*.jpeg";
                    openFileDialog.Title = "Select Floor Plan Image";
                    openFileDialog.Multiselect = false;

                    if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                    {
                        return openFileDialog.FileName;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FloorPlanImporter: Error opening file dialog: {e.Message}");
            }
            
            return "";
#endif
        }

        /// <summary>
        /// Coroutine to load and apply the image texture
        /// </summary>
        private IEnumerator LoadImageCoroutine(string filePath)
        {
            Debug.Log($"FloorPlanImporter: Loading image from {filePath}");

            // Read file bytes
            byte[] imageData;
            try
            {
                imageData = File.ReadAllBytes(filePath);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FloorPlanImporter: Error reading file: {e.Message}");
                yield break;
            }

            // Create texture and load image data
            Texture2D newTexture = new Texture2D(2, 2, TextureFormat.RGB24, false);
            newTexture.name = Path.GetFileNameWithoutExtension(filePath);

            if (newTexture.LoadImage(imageData))
            {
                // Resize if too large
                if (newTexture.width > maxTextureSize || newTexture.height > maxTextureSize)
                {
                    newTexture = ResizeTexture(newTexture, maxTextureSize);
                }

                // Apply to material
                groundMaterial.SetTexture(texturePropertyName, newTexture);
                
                Debug.Log($"FloorPlanImporter: Successfully loaded floor plan '{newTexture.name}' ({newTexture.width}x{newTexture.height})");
                
                // Optional: Adjust ground plane scale to match image aspect ratio
                AdjustGroundPlaneScale(newTexture);
            }
            else
            {
                Debug.LogError("FloorPlanImporter: Failed to load image data into texture");
                Destroy(newTexture);
            }

            yield return null;
        }

        /// <summary>
        /// Resize texture if it exceeds maximum size
        /// </summary>
        private Texture2D ResizeTexture(Texture2D originalTexture, int maxSize)
        {
            int newWidth = originalTexture.width;
            int newHeight = originalTexture.height;

            // Calculate new dimensions maintaining aspect ratio
            if (newWidth > maxSize || newHeight > maxSize)
            {
                float aspectRatio = (float)newWidth / newHeight;
                
                if (newWidth > newHeight)
                {
                    newWidth = maxSize;
                    newHeight = Mathf.RoundToInt(maxSize / aspectRatio);
                }
                else
                {
                    newHeight = maxSize;
                    newWidth = Mathf.RoundToInt(maxSize * aspectRatio);
                }
            }

            // Create render texture and resize
            RenderTexture rt = RenderTexture.GetTemporary(newWidth, newHeight);
            Graphics.Blit(originalTexture, rt);
            
            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = rt;
            
            Texture2D resizedTexture = new Texture2D(newWidth, newHeight);
            resizedTexture.ReadPixels(new Rect(0, 0, newWidth, newHeight), 0, 0);
            resizedTexture.Apply();
            
            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(rt);
            
            // Clean up original
            Destroy(originalTexture);
            
            Debug.Log($"FloorPlanImporter: Resized texture to {newWidth}x{newHeight}");
            return resizedTexture;
        }

        /// <summary>
        /// Optionally adjust ground plane scale to match image aspect ratio
        /// </summary>
        private void AdjustGroundPlaneScale(Texture2D texture)
        {
            if (groundPlaneRenderer == null) return;

            float aspectRatio = (float)texture.width / texture.height;
            Vector3 currentScale = groundPlaneRenderer.transform.localScale;
            
            // Adjust X scale to match aspect ratio, keep Y and Z the same
            groundPlaneRenderer.transform.localScale = new Vector3(
                currentScale.y * aspectRatio, 
                currentScale.y, 
                currentScale.z
            );
            
            Debug.Log($"FloorPlanImporter: Adjusted ground plane scale to match aspect ratio {aspectRatio:F2}");
        }

        /// <summary>
        /// Reset to original floor plan texture
        /// </summary>
        public void ResetToOriginalFloorPlan()
        {
            if (groundMaterial != null && originalTexture != null)
            {
                groundMaterial.SetTexture(texturePropertyName, originalTexture);
                Debug.Log("FloorPlanImporter: Reset to original floor plan");
            }
        }

        /// <summary>
        /// Get current floor plan texture info for debugging
        /// </summary>
        public void LogCurrentTextureInfo()
        {
            if (groundMaterial != null)
            {
                Texture currentTexture = groundMaterial.GetTexture(texturePropertyName);
                if (currentTexture != null)
                {
                    Debug.Log($"Current floor plan: {currentTexture.name} ({currentTexture.width}x{currentTexture.height})");
                }
                else
                {
                    Debug.Log("No floor plan texture currently assigned");
                }
            }
        }
    }
}
