using UnityEngine;
using UnityEngine.AddressableAssets; // <-- Add this line to use Addressables

[CreateAssetMenu(fileName = "NewAgentProfile", menuName = "Factory Simulation/Agent Profile")]
public class AgentProfile : ScriptableObject
{
    [Header("Agent Identity")]
    public string agentModelName = "Default Robot";
    
    // --- THIS IS THE "ABANG" ---
    [<PERSON><PERSON>("Visual & Physical Properties")]
    [Tooltip("Addressable link to the robot's visual prefab. Allows for custom models post-MVP.")]
    public AssetReferenceGameObject robotVisualsPrefabReference; // We won't use this for the MVP, but it's ready for V2.
    // We could also add fields for physical dimensions (width, height) here later.

    [Header("Performance Metrics")]
    [Tooltip("Movement speed in meters per second.")]
    public float moveSpeed = 2.0f;
    
    [Tooltip("Rotational speed in degrees per second.")]
    public float turnSpeed = 120.0f;

    [Tooltip("Time in seconds to complete one standard task execution.")]
    public float taskExecutionTime = 5.0f;

    [<PERSON><PERSON>("Energy System")]
    [Tooltip("Total battery life in simulated minutes of continuous operation.")]
    public float batteryCapacityMinutes = 120.0f;

    [Tooltip("The battery percentage (from 0.0 to 1.0) at which the agent will decide to charge.")]
    [Range(0.0f, 1.0f)]
    public float chargeThreshold = 0.2f;

    [Tooltip("How many 'minutes' of battery life are restored per real-time second of charging.")]
    public float chargeRate = 2.0f;
}