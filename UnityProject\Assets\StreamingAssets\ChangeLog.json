{"version": "1.0", "project_name": "YourUnityProject", "changes": [{"date": "2025-07-13", "version": "1.0.1", "type": "enhancement", "component": "CameraController", "description": "Implemented proportional pan speed based on zoom level", "details": ["Pan speed now scales with camera distance from target", "Faster panning when zoomed out for quick navigation", "Slower panning when zoomed in for precise control", "Uses zoom factor calculation: panSpeed * (0.1f + zoomFactor * 0.9f)", "Maintains minimum 10% pan speed when fully zoomed in"], "files_modified": ["Assets/_Project/Scripts/CameraController.cs"], "author": "Augment Agent"}, {"date": "2025-07-13", "version": "1.0.2", "type": "enhancement", "component": "ObjectPlacementManager", "description": "Implemented automatic calibration completion for demo purposes", "details": ["Added knownRealWorldDistance public field for inspector configuration", "Modified HandleCalibrationInput() to automatically complete calibration after second point selection", "Removed dependency on UI input for calibration completion", "Streamlined calibration workflow for demo and development use"], "files_modified": ["Assets/_Project/Scripts/ObjectPlacementManager.cs"], "author": "Augment Agent"}, {"date": "2025-07-13", "version": "1.0.3", "type": "bugfix", "component": "<PERSON><PERSON><PERSON><PERSON>, ObjectPlacementManager", "description": "Fixed robot selection failure after camera transformations", "details": ["Fixed inconsistent camera reference usage in SelectionManager", "Replaced Camera.main calls with cached mainCamera reference", "Added camera reference validation and fallback logic", "Added debug logging and ray visualization for selection troubleshooting", "Improved camera reference reliability in ObjectPlacementManager", "Resolved issue where robots became unselectable after pan/zoom/orbit operations"], "files_modified": ["Assets/_Project/Scripts/SelectionManager.cs", "Assets/_Project/Scripts/ObjectPlacementManager.cs"], "author": "Augment Agent"}, {"date": "2025-07-13", "version": "1.0.4", "type": "feature", "component": "FloorPlanManager", "description": "Implemented professional two-step floor plan management system", "details": ["Created dedicated FloorPlanManager for floor plan library management", "Two-step workflow: Import to library, then select and apply to scene", "Floor plans stored persistently in StreamingAssets/FloorPlans folder", "Dropdown UI for selecting from available floor plans", "Support for both default (built-in) and imported floor plans", "Delete functionality with confirmation dialog for imported floor plans", "Automatic texture resizing and memory management", "Professional file dialog integration for PNG/JPG import", "Maintains existing scale calibration and object placement compatibility"], "files_modified": ["Assets/_Project/Scripts/FloorPlanManager.cs", "Assets/_Project/Scripts/ObjectPlacementManager.cs"], "author": "Augment Agent"}], "statistics": {"total_changes": 4, "last_updated": "2025-07-13", "llm_model": "<PERSON> 4"}}