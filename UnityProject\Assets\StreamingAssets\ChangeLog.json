{"version": "1.0", "project_name": "YourUnityProject", "changes": [{"date": "2025-07-13", "version": "1.0.1", "type": "enhancement", "component": "CameraController", "description": "Implemented proportional pan speed based on zoom level", "details": ["Pan speed now scales with camera distance from target", "Faster panning when zoomed out for quick navigation", "Slower panning when zoomed in for precise control", "Uses zoom factor calculation: panSpeed * (0.1f + zoomFactor * 0.9f)", "Maintains minimum 10% pan speed when fully zoomed in"], "files_modified": ["Assets/_Project/Scripts/CameraController.cs"], "author": "Augment Agent"}, {"date": "2025-07-13", "version": "1.0.2", "type": "enhancement", "component": "ObjectPlacementManager", "description": "Implemented automatic calibration completion for demo purposes", "details": ["Added knownRealWorldDistance public field for inspector configuration", "Modified HandleCalibrationInput() to automatically complete calibration after second point selection", "Removed dependency on UI input for calibration completion", "Streamlined calibration workflow for demo and development use"], "files_modified": ["Assets/_Project/Scripts/ObjectPlacementManager.cs"], "author": "Augment Agent"}], "statistics": {"total_changes": 2, "last_updated": "2025-07-13", "llm_model": "<PERSON> 4"}}