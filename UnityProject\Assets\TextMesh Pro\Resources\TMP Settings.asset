%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2705215ac5b84b70bacc50632be6e391, type: 3}
  m_Name: TMP Settings
  m_EditorClassIdentifier: 
  assetVersion: 2
  m_TextWrappingMode: 1
  m_enableKerning: 1
  m_ActiveFontFeatures: 00000000
  m_enableExtraPadding: 0
  m_enableTintAllSprites: 0
  m_enableParseEscapeCharacters: 1
  m_EnableRaycastTarget: 1
  m_GetFontFeaturesAtRuntime: 1
  m_missingGlyphCharacter: 0
  m_ClearDynamicDataOnBuild: 1
  m_warningsDisabled: 0
  m_defaultFontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_defaultFontAssetPath: Fonts & Materials/
  m_defaultFontSize: 36
  m_defaultAutoSizeMinRatio: 0.5
  m_defaultAutoSizeMaxRatio: 2
  m_defaultTextMeshProTextContainerSize: {x: 20, y: 5}
  m_defaultTextMeshProUITextContainerSize: {x: 200, y: 50}
  m_autoSizeTextContainer: 0
  m_IsTextObjectScaleStatic: 0
  m_fallbackFontAssets: []
  m_matchMaterialPreset: 1
  m_HideSubTextObjects: 0
  m_defaultSpriteAsset: {fileID: 11400000, guid: c41005c129ba4d66911b75229fd70b45,
    type: 2}
  m_defaultSpriteAssetPath: Sprite Assets/
  m_enableEmojiSupport: 1
  m_MissingCharacterSpriteUnicode: 0
  m_EmojiFallbackTextAssets: []
  m_defaultColorGradientPresetsPath: Color Gradient Presets/
  m_defaultStyleSheet: {fileID: 11400000, guid: f952c082cb03451daed3ee968ac6c63e,
    type: 2}
  m_StyleSheetsResourcePath: 
  m_leadingCharacters: {fileID: 4900000, guid: d82c1b31c7e74239bff1220585707d2b, type: 3}
  m_followingCharacters: {fileID: 4900000, guid: fade42e8bc714b018fac513c043d323b,
    type: 3}
  m_UseModernHangulLineBreakingRules: 0
