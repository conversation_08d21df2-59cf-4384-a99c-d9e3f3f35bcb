using UnityEngine;

namespace Factory.Simulation
{
    public class CameraController : MonoBeh<PERSON>our
    {
        [Header("Target")]
        [Tooltip("The point the camera will orbit around. This will be moved dynamically.")]
        [SerializeField] private Transform target;

        [<PERSON>er("Speeds")]
        [SerializeField] private float zoomSpeed = 10f;
        [SerializeField] private float panSpeed = 40f;
        [SerializeField] private float orbitSpeed = 10f;

        [Header("Limits")]
        [SerializeField] private float minZoomDistance = 5f;
        [SerializeField] private float maxZoomDistance = 150f;

        private Vector3 lastMousePosition;
        private Camera mainCamera;

        private void Awake()
        {
            mainCamera = GetComponent<Camera>();
        }

        private void LateUpdate()
        {
            if (target == null) return;

            // Handle inputs. Order matters slightly for feel.
            HandlePan();
            HandleOrbit();
            HandleZoom();
        }

        private void HandlePan()
        {
            if (Input.GetMouseButtonDown(2))
            {
                lastMousePosition = Input.mousePosition;
            }

            if (Input.GetMouseButton(2))
            {
                Vector3 delta = Input.mousePosition - lastMousePosition;
                // Calculate movement based on camera orientation, scaled by deltaTime for smoothness
                Vector3 move = (transform.right * -delta.x + transform.up * -delta.y) * panSpeed * Time.deltaTime;
                
                transform.position += move;
                target.position += move;

                lastMousePosition = Input.mousePosition;
            }
        }

        private void HandleOrbit()
        {
            if (Input.GetKey(KeyCode.LeftAlt) && Input.GetMouseButton(0))
            {
                float horizontal = Input.GetAxis("Mouse X") * orbitSpeed;
                float vertical = Input.GetAxis("Mouse Y") * orbitSpeed;

                // Rotate the camera around the target's position
                transform.RotateAround(target.position, Vector3.up, horizontal);
                transform.RotateAround(target.position, transform.right, -vertical);
            }
        }

        private void HandleZoom()
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");

            if (Mathf.Abs(scroll) > 0.01f)
            {
                // --- DEFINITIVE ZOOM LOGIC ---
                // This logic moves the camera towards the mouse cursor's position in 3D space.

                // 1. Get a ray from the mouse cursor
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

                // 2. Calculate the move amount. We use a factor of the current distance
                //    to make zooming feel consistent whether you are far away or close up.
                float currentDistance = Vector3.Distance(transform.position, target.position);
                float moveAmount = scroll * zoomSpeed * currentDistance * Time.deltaTime;

                // 3. Check if the new position would be within our limits
                if (currentDistance - moveAmount < minZoomDistance || currentDistance - moveAmount > maxZoomDistance)
                {
                    return; // Abort if we would go past the zoom limits
                }

                // 4. Move the camera along the ray from the mouse cursor
                transform.position += ray.direction * moveAmount;
            }
        }
    }
}