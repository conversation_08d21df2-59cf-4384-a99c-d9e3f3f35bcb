using UnityEngine;
using UnityEngine.AI;

namespace Factory.Simulation
{
    public class RobotController : MonoBehaviour
    {
        // --- State Machine ---
        private enum RobotState { IDLE, MOVING_TO_TASK, PERFORMING_TASK }
        private RobotState currentState;

        // --- Dependencies & Components ---
        private NavMeshAgent navMeshAgent;          // The "Driver" for our simulation
        private SelectionHighlight selectionHighlight; // The "Visual Feedback" component
        private AgentProfile activeProfile;

        // --- State Variables ---
        private float taskTimer;

        //======================================================================
        // INITIALIZATION & UNITY LIFECYCLE
        //======================================================================

        private void Awake()
        {
            navMeshAgent = GetComponent<NavMeshAgent>();
            selectionHighlight = GetComponent<SelectionHighlight>();
        }

        public void Initialize(AgentProfile profile)
        {
            if (profile == null)
            {
                Debug.LogError($"Robot '{gameObject.name}' received a null profile. Initialization failed.", this);
                return;
            }

            activeProfile = profile;
            gameObject.name = activeProfile.agentModelName;
            
            // Configure the "Driver" with data from the profile
            navMeshAgent.speed = activeProfile.moveSpeed;
            navMeshAgent.angularSpeed = activeProfile.turnSpeed;

            // Kickstart the brain
            ChangeState(RobotState.IDLE);
            Debug.Log($"Robot '{gameObject.name}' initialized with profile '{profile.name}'. Starting task loop.");
        }

        private void Update()
        {
            // The brain only runs if it has been properly initialized.
            if (activeProfile == null) return;

            // Run the decision-making logic for the current state.
            OnStateUpdate();
        }

        //======================================================================
        // HIGH-LEVEL CONTROLLER (The "Brain" - State Machine Logic)
        //======================================================================

        private void OnStateUpdate()
        {
            switch (currentState)
            {
                case RobotState.IDLE:
                    // Decision: We are idle, so we must find new work.
                    RequestAndStartNewTask();
                    break;
                
                case RobotState.MOVING_TO_TASK:
                    // Decision: Have we arrived at our destination?
                    if (HasArrived())
                    {
                        ChangeState(RobotState.PERFORMING_TASK);
                    }
                    break;

                case RobotState.PERFORMING_TASK:
                    // Decision: Is the current work finished?
                    taskTimer -= Time.deltaTime;
                    if (taskTimer <= 0)
                    {
                        CompleteTask();
                        ChangeState(RobotState.IDLE); // Return to idle to find more work.
                    }
                    break;
            }
        }

        private void ChangeState(RobotState newState)
        {
            currentState = newState;
            // Setup logic for entering the new state.
            switch (currentState)
            {
                case RobotState.PERFORMING_TASK:
                    // When work begins, set the timer based on our profile's efficiency.
                    taskTimer = activeProfile.taskExecutionTime;
                    break;
                // Other states require no special setup for now.
                case RobotState.IDLE:
                case RobotState.MOVING_TO_TASK:
                    break;
            }
        }

        //======================================================================
        // LOW-LEVEL DRIVER COMMANDS & QUERIES (The "Muscles" - Actions & Status)
        //======================================================================
        
        private void RequestAndStartNewTask()
        {
            // High-level action: get a task and tell the driver to go.
            Vector3 destination = TaskManager.Instance.RequestTask();
            MoveTo(destination);
            ChangeState(RobotState.MOVING_TO_TASK);
        }
        
        public void MoveTo(Vector3 destination)
        {
            // Command issued to the simulation "driver" (NavMeshAgent).
            if (!navMeshAgent.enabled) navMeshAgent.enabled = true;
            navMeshAgent.isStopped = false;
            navMeshAgent.SetDestination(destination);
        }

        private bool HasArrived()
        {
            // Query the status of the "driver".
            return navMeshAgent.enabled && !navMeshAgent.pathPending && navMeshAgent.remainingDistance <= navMeshAgent.stoppingDistance;
        }

        private void CompleteTask()
        {
            // Report our success to the central KPI system.
            KPI_Manager.Instance.TrackTaskCompletion(gameObject.name);
        }
        
        //======================================================================
        // PUBLIC API & MANUAL OVERRIDES (Interface for External Systems)
        //======================================================================
        
        public void SetSelected(bool selected)
        {
            if (selectionHighlight != null)
            {
                selectionHighlight.SetHighlighted(selected);
            }
        }

        public void BeginManualTranslation()
        {
            // When a user takes manual control, disable the autonomous driver.
            if (navMeshAgent.enabled)
            {
                navMeshAgent.enabled = false;
            }
        }

        public void EndManualTranslation()
        {
            // When the user releases control, re-enable the driver at the new position.
            if (!navMeshAgent.enabled)
            {
                navMeshAgent.enabled = true;
                navMeshAgent.Warp(transform.position);
            }
        }
    }
}