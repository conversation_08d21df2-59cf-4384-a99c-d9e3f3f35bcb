using UnityEngine;

namespace Factory.Simulation
{
    public class SelectionManager : MonoBehaviour
    {
        [Header("Dependencies")]
        [SerializeField] private ObjectPlacementManager objectPlacementManager;

        // --- NEW: LayerMask for efficient raycasting ---
        [Header("Settings")]
        [Tooltip("Set this to 'Default' and 'Ground' layers.")]
        [SerializeField] private LayerMask selectionLayerMask;

        private GameObject currentlySelectedObject;
        private RobotController selectedRobotController;
        private Camera mainCamera;
        private Quaternion objectRotation;

        private void Start()
        {
            mainCamera = Camera.main;
        }

        private void Update()
        {
            if (currentlySelectedObject != null && GameManager.Instance.CurrentMode == EGameMode.Selection && (Input.GetKeyDown(KeyCode.Delete) || Input.GetKeyDown(KeyCode.Backspace)))
            {
                DeleteCurrentSelection();
                return;
            }

            if (Input.GetMouseButtonDown(1) && selectedRobotController != null)
            {
                IssueMoveCommand();
            }

            switch (GameManager.Instance.CurrentMode)
            {
                case EGameMode.Selection:
                    HandleSelectionInput();
                    break;
                case EGameMode.Translate:
                    HandleTranslationInput();
                    break;
            }
        }

        private void HandleSelectionInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                // --- MODIFIED: Added layerMask parameter ---
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, selectionLayerMask))
                {
                    Selectable selectable = hit.collider.GetComponentInParent<Selectable>();
                    if (selectable != null)
                    {
                        SelectObject(selectable.gameObject);
                        GameManager.Instance.SetMode(EGameMode.Translate);
                    }
                    else
                    {
                        DeselectObject();
                    }
                }
                else
                {
                    DeselectObject();
                }
            }
        }

        private void HandleTranslationInput()
        {
            if (Input.GetMouseButtonUp(0))
            {
                // --- MODIFIED: On mouse up, tell the robot its drag has ended. ---
                if (selectedRobotController != null)
                {
                    selectedRobotController.EndManualTranslation();
                }
                GameManager.Instance.SetMode(EGameMode.Selection);
                return;
            }

            // --- MODIFIED: Added layerMask parameter, but only for the 'Ground' layer ---
            // We create a specific mask here for this one action.
            int groundLayerOnly = LayerMask.GetMask("Ground");
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, groundLayerOnly))
            {
                Vector3 targetPosition = hit.point;
                if (currentlySelectedObject.GetComponent<RobotController>() == null)
                {
                    targetPosition = GetSnappedPosition(hit.point);
                }
                currentlySelectedObject.transform.position = targetPosition;
            }

            if (Input.GetKeyDown(KeyCode.R))
            {
                float rotationAmount = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift) ? -90f : 90f;
                objectRotation *= Quaternion.Euler(0, rotationAmount, 0);
                currentlySelectedObject.transform.rotation = objectRotation;
            }
        }

        private void SelectObject(GameObject objectToSelect)
        {
            if (currentlySelectedObject == objectToSelect) return;
            DeselectObject();

            currentlySelectedObject = objectToSelect;
            objectRotation = currentlySelectedObject.transform.rotation;
            Debug.Log($"Selected: {objectToSelect.name}");
            
            if (currentlySelectedObject.TryGetComponent(out selectedRobotController))
            {
                // --- MODIFIED: Call the new method to begin the drag. ---
                selectedRobotController.BeginManualTranslation();
                selectedRobotController.SetSelected(true);
                if (selectedRobotController.TryGetComponent(out UnityEngine.AI.NavMeshAgent agent))
                {
                    agent.avoidancePriority = 10;
                }
            }
            else if (currentlySelectedObject.TryGetComponent(out SelectionHighlight highlight))
            {
                highlight.SetHighlighted(true);
            }
        }

        private void DeselectObject()
        {
            if (currentlySelectedObject == null) return;
            Debug.Log($"Deselected: {currentlySelectedObject.name}");
            
            if (selectedRobotController != null)
            {
                // --- MODIFIED: When deselecting, also ensure the drag state is ended. ---
                selectedRobotController.EndManualTranslation();
                selectedRobotController.SetSelected(false);
                if (selectedRobotController.TryGetComponent(out UnityEngine.AI.NavMeshAgent agent))
                {
                    agent.avoidancePriority = 50;
                }
            }
            else if (currentlySelectedObject.TryGetComponent(out SelectionHighlight highlight))
            {
                highlight.SetHighlighted(false);
            }

            currentlySelectedObject = null;
            selectedRobotController = null;
        }

        private void DeleteCurrentSelection()
        {
            if (currentlySelectedObject == null) return;
            Debug.Log($"DELETED: {currentlySelectedObject.name}");
            Destroy(currentlySelectedObject);
            currentlySelectedObject = null;
            selectedRobotController = null;
        }

        private void IssueMoveCommand()
        {
            // --- MODIFIED: Added layerMask parameter ---
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, selectionLayerMask))
            {
                // We still check the tag to be sure we're not moving onto an obstacle.
                if (hit.collider.CompareTag("Ground"))
                {
                    selectedRobotController.MoveTo(hit.point);
                }
            }
        }

        private Vector3 GetSnappedPosition(Vector3 originalWorldPosition)
        {
            if (objectPlacementManager == null) return originalWorldPosition;
            float gridSize = objectPlacementManager.gridSize;
            Transform container = objectPlacementManager.environmentContainer;
            Vector3 localPos = container.InverseTransformPoint(originalWorldPosition);
            float snappedX = Mathf.Round(localPos.x / gridSize) * gridSize;
            float snappedZ = Mathf.Round(localPos.z / gridSize) * gridSize;
            Vector3 snappedLocalPos = new Vector3(snappedX, localPos.y, snappedZ);
            return container.TransformPoint(snappedLocalPos);
        }
    }
}